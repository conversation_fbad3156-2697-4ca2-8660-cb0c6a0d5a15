// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

// Initial positions for each letter (scattered around)
const initialPositions = [
  { x: -800, y: -400, rotation: -45 }, // W
  { x: 800, y: -300, rotation: 60 }, // e
  { x: -700, y: 200, rotation: 30 }, // D
  { x: 900, y: 300, rotation: -75 }, // e
  { x: 0, y: -600, rotation: 90 }, // s
];

// Final positions (center with proper spacing to spell "WeDes")
const finalPositions = [
  { x: -240, y: 0, rotation: 0 }, // W
  { x: -120, y: 0, rotation: 0 }, // e
  { x: 0, y: 0, rotation: 0 }, // D
  { x: 120, y: 0, rotation: 0 }, // e
  { x: 240, y: 0, rotation: 0 }, // s
];

const letters = document.querySelectorAll(".letter");

// Set initial positions and properties for all letters
letters.forEach((letter, index) => {
  gsap.set(letter, {
    x: initialPositions[index].x,
    y: initialPositions[index].y,
    rotation: initialPositions[index].rotation,
    scale: 0.5,
    opacity: 0,
  });
});

// Create a single timeline for all letters to ensure perfect synchronization
const tl = gsap.timeline({
  scrollTrigger: {
    trigger: ".container",
    start: "top topp",
    end: "bottom top",
    scrub: 2, // Smooth scrubbing
    pin: true,
    markers: true, // Set to true if you want to see the trigger points
    onComplete: () => {
      // Final bounce effect for all letters
      gsap.to(letters, {
        scale: 1.1,
        duration: 0.5,
        ease: "back.out(1.7)",
        yoyo: true,
        repeat: 1,
      });
    },
  },
});

// Add all letter animations to the same timeline
letters.forEach((letter, index) => {
  tl.to(
    letter,
    {
      x: finalPositions[index].x,
      y: finalPositions[index].y,
      rotation: finalPositions[index].rotation,
      scale: 1,
      opacity: 1,
      duration: 3,
      ease: "power2.inOut",
    },
    0
  ); // The "0" ensures all animations start at the same time
});

// Optional: Add hover effects for individual letters
letters.forEach((letter, index) => {
  letter.addEventListener("mouseenter", () => {
    gsap.to(letter, {
      scale: 1.3,
      rotation: finalPositions[index].rotation + 10,
      duration: 0.3,
      ease: "back.out(1.7)",
    });
  });

  letter.addEventListener("mouseleave", () => {
    gsap.to(letter, {
      scale: 1,
      rotation: finalPositions[index].rotation,
      duration: 0.3,
      ease: "back.out(1.7)",
    });
  });
});

window.addEventListener("load", () => {
  gsap
    .timeline({
      scrollTrigger: {
        trigger: ".wrapper",
        start: "top top",
        end: "+=150%",
        pin: true,
        scrub: true,
        markers: true,
      },
    })
    .to("img", {
      scale: 2,
      z: 350,
      transformOrigin: "center center",
      ease: "power1.inOut",
    })
    .to(
      ".section.hero",
      {
        scale: 1.1,
        transformOrigin: "center center",
        ease: "power1.inOut",
      },
      "<"
    );
});
