
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .wrapper,
  .content {
    position: relative;
    width: 100%;
    z-index: 1;
  }
  
  .content {
    overflow-x: hidden;
  }
  
  .content .section {
    width: 100%;
    height: 100vh;
  }
  
  .content .section.hero {
    background-image: url(https://images.unsplash.com/photo-1589848315097-ba7b903cc1cc?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
  }
  
  .image-container {
    width: 100%;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 2;
    perspective: 500px;
    overflow: hidden;
  }
  
  .image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
  }

/* Styles moved from inline <style> tag in index.html */
body {
    margin: 0;
    padding: 0;
    min-height: 200vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Arial', sans-serif;
}

.scroll-section {
    height: 150vh;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.container {
    position: relative;
    width: 800px;
    height: 600px;
    border-radius: 20px;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
}

.letter {
    position: absolute;
    font-size: 120px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    user-select: none;
    cursor: pointer;
}

.position-marker {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #ff6b6b;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.7;
}

.start-marker {
    background: #4ecdc4;
}

.end-marker {
    background: #ff6b6b;
}

.info {
    position: fixed;
    top: 20px;
    left: 20px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    line-height: 1.5;
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(5px);
    z-index: 100;
}

.scroll-indicator {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    animation: bounce 2s infinite;
    z-index: 100;
}

