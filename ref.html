<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <style>
      <!-- :root {
        --bg-gradient: linear-gradient(
          180deg,
          rgba(0, 0, 0, 0.6) 0%,
          rgba(0, 0, 0, 0.1) 100%
        );
      } -->

      * {
        box-sizing: border-box;
        user-select: none;
      }

      smoothlist {
        margin: 0;
        padding: 0;
        height: 100vh;
        background: black;
        text-transform: uppercase;
      }

      h2 {
        font-size: clamp(1rem, 8vw, 10rem);
        font-weight: 600;
        text-align: center;
        margin-right: -0.5em;
        width: 90vw;
        max-width: 1200px;
        text-transform: none;
      }

      section {
        height: 100%;
        width: 100%;
        top: 0;
        position: fixed;
        visibility: hidden;
        color:antiquewhite;
      }

      section .outer,
      section .inner {
        width: 100%;
        height: 100%;
        overflow-y: hidden;
      }

      section .bg {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        background-size: cover;
        background-position: center;
      }

      section .bg h2 {
        z-index: 999;
      }

      section .bg .clip-text {
        overflow: hidden;
      }

      .first .bg {
        background-image: var(--bg-gradient),
          url("https://assets.codepen.io/16327/site-landscape-1.jpg");
      }

      .second .bg {
        background-image: var(--bg-gradient),
          url("https://assets.codepen.io/16327/site-landscape-5.jpeg");
      }

      .third .bg {
        background-image: var(--bg-gradient),
          url("https://assets.codepen.io/16327/site-landscape-2.jpg");
      }

      .fourth .bg {
        background-image: var(--bg-gradient),
          url("https://assets.codepen.io/16327/site-landscape-6.jpg");
      }

      .fifth .bg {
        background-image: var(--bg-gradient),
          url("https://assets.codepen.io/16327/site-landscape-8.jpg");
        background-position: 50% 45%;
      }

      h2 * {
        will-change: transform;
      }
    </style>
    <!--
Sections animate in and out on scroll. Scroll up or down and the sections will wrap around after hitting the start or end. Uses GSAP for the animations.
-->
<div class="smoothlist">
  <section class="first">
    <div class="outer">
      <div class="inner">
        <div class="bg one">
          <h2 class="section-heading">Scroll down</h2>
        </div>
      </div>
    </div>
  
  </section>
  <section class="second">
    <div class="outer">
      <div class="inner">
        <div class="bg">
          <h2 class="section-heading">Animated with GSAP</h2>
        </div>
      </div>
    </div>
  </section>
  <section class="third">
    <div class="outer">
      <div class="inner">
        <div class="bg">
          <h2 class="section-heading">GreenSock</h2>
        </div>
      </div>
    </div>
  </section>
  <section class="fourth">
    <div class="outer">
      <div class="inner">
        <div class="bg">
          <h2 class="section-heading">Animation platform</h2>
        </div>
      </div>
    </div>
  </section>
  <section class="fifth">
    <div class="outer">
      <div class="inner">
        <div class="bg">
          <h2 class="section-heading">Keep scrolling</h2>
        </div>
      </div>
    </div>
  </section>
</div>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/Observer.min.js"></script>
  <script src="https://assets.codepen.io/16327/SplitText3.min.js"></script>

  <script>
    gsap.registerPlugin(Observer);

    let sections = document.querySelectorAll("section"),
      images = document.querySelectorAll(".bg"),
      headings = gsap.utils.toArray(".section-heading"),
      outerWrappers = gsap.utils.toArray(".outer"),
      innerWrappers = gsap.utils.toArray(".inner"),
      splitHeadings = headings.map(heading => new SplitText(heading, { type: "chars,words,lines", linesClass: "clip-text" })),
      currentIndex = -1,
      animating;
    
    gsap.set(outerWrappers, { yPercent: 100 });
    gsap.set(innerWrappers, { yPercent: -100 });
    
    function gotoSection(index, direction) {
        // clamp index between 0 and sections.length - 1
        if (index < 0 || index >= sections.length) {
          return; // do nothing if out of range
        }
      
        animating = true;
        let fromTop = direction === -1,
            dFactor = fromTop ? -1 : 1,
            tl = gsap.timeline({
              defaults: { duration: 1.25, ease: "power1.inOut" },
              onComplete: () => animating = false
            });
      
        if (currentIndex >= 0) {
          gsap.set(sections[currentIndex], { zIndex: 0 });
          tl.to(images[currentIndex], { yPercent: -15 * dFactor })
            .set(sections[currentIndex], { autoAlpha: 0 });
        }
      
        gsap.set(sections[index], { autoAlpha: 1, zIndex: 1 });
        tl.fromTo([outerWrappers[index], innerWrappers[index]], { 
            yPercent: i => i ? -100 * dFactor : 100 * dFactor
          }, { 
            yPercent: 0 
          }, 0)
          .fromTo(images[index], { yPercent: 15 * dFactor }, { yPercent: 0 }, 0)
          .fromTo(splitHeadings[index].chars, { 
              autoAlpha: 0, 
              yPercent: 150 * dFactor
          }, {
              autoAlpha: 1,
              yPercent: 0,
              duration: 1,
              ease: "power2",
              stagger: {
                each: 0.02,
                from: "random"
              }
            }, 0.2);
      
        currentIndex = index;
      }
      
    
    Observer.create({
      type: "wheel,touch,pointer",
      wheelSpeed: -1,
      onDown: () => !animating && gotoSection(currentIndex - 1, -1),
      onUp: () => !animating && gotoSection(currentIndex + 1, 1),
      tolerance: 10,
      preventDefault: true
    });
    
    gotoSection(0, 1);
    
    // original: https://codepen.io/BrianCross/pen/PoWapLP
    // horizontal version: https://codepen.io/GreenSock/pen/xxWdeMK
    </script>
  
</body>


</html>